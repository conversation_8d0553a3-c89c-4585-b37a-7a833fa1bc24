import pandas as pd

def extract_sheets():
    """
    从指定的Excel文件中提取第一个和第三个工作表，
    并将它们分别保存为CSV文件。
    """
    try:
        # 定义输入文件的路径
        excel_file_path = '原油期权日行情历史数据20250721.xlsx'

        # 读取Excel文件以获取其所有工作表
        xls = pd.ExcelFile(excel_file_path)
        sheet_names = xls.sheet_names

        # 检查工作表数量是否足够
        if len(sheet_names) < 1:
            print(f"错误: 文件 '{excel_file_path}' 中没有找到任何工作表。")
            return
            
        # 提取并保存第一个工作表
        first_sheet_name = sheet_names[0]
        df1 = pd.read_excel(xls, sheet_name=first_sheet_name)
        output_filename_1 = f"{first_sheet_name}.csv"
        df1.to_csv(output_filename_1, index=False, encoding='utf-8-sig')
        print(f"已成功将工作表 '{first_sheet_name}' 提取并保存到 '{output_filename_1}'")

        # 提取并保存第三个工作表
        third_sheet_name = sheet_names[2]
        df3 = pd.read_excel(xls, sheet_name=third_sheet_name)
        output_filename_3 = f"{third_sheet_name}.csv"
        df3.to_csv(output_filename_3, index=False, encoding='utf-8-sig')
        print(f"已成功将工作表 '{third_sheet_name}' 提取并保存到 '{output_filename_3}'")

    except FileNotFoundError:
        print(f"错误: 未找到文件 '{excel_file_path}'。请确保文件路径正确。")
    except Exception as e:
        print(f"处理过程中发生错误: {e}")

if __name__ == "__main__":
    extract_sheets()
