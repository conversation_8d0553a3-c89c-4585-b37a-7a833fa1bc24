import matplotlib.pyplot as plt
import numpy as np
import random
from matplotlib.colors import ListedColormap

# 设置中文字体
plt.rcParams['font.family'] = 'SimHei'
plt.rcParams['axes.unicode_minus'] = False

def generate_target_maze():
    """生成目标迷宫 - 使用递归回溯算法的特定变体（正确答案）"""
    width, height = 21, 21
    random.seed(123)  # 使用特定种子
    np.random.seed(123)
    
    maze = np.ones((height, width), dtype=int)
    
    # 递归回溯的特殊变体：优先选择最长路径
    directions = [(-2, 0), (0, 2), (2, 0), (0, -2)]
    
    def is_valid(x, y):
        return 0 <= x < height and 0 <= y < width
    
    def carve_path(x, y):
        maze[x, y] = 0
        
        # 计算每个方向的潜在路径长度，优先选择能创造更长路径的方向
        dir_scores = []
        for dx, dy in directions:
            nx, ny = x + dx, y + dy
            if is_valid(nx, ny) and maze[nx, ny] == 1:
                # 计算这个方向的"深度"得分
                score = abs(nx - height//2) + abs(ny - width//2)  # 距离中心的距离
                dir_scores.append((score, dx, dy))
        
        # 按得分排序，优先选择得分高的方向
        dir_scores.sort(reverse=True)
        
        for score, dx, dy in dir_scores:
            nx, ny = x + dx, y + dy
            if is_valid(nx, ny) and maze[nx, ny] == 1:
                maze[x + dx//2, y + dy//2] = 0
                carve_path(nx, ny)
    
    carve_path(1, 1)
    
    # 添加一些特殊的连接以增加复杂性
    special_connections = [(3, 5), (7, 9), (11, 13), (15, 17)]
    for x, y in special_connections:
        if maze[x, y] == 1 and (maze[x-1, y] == 0 or maze[x+1, y] == 0 or 
                                maze[x, y-1] == 0 or maze[x, y+1] == 0):
            maze[x, y] = 0
    
    return maze

def visualize_maze():
    """可视化迷宫"""
    maze = generate_target_maze()
    
    fig, ax = plt.subplots(1, 1, figsize=(10, 10))
    
    # 标记起点和终点
    display_maze = maze.copy().astype(float)
    display_maze[1, 1] = 2  # 起点
    display_maze[19, 19] = 3  # 终点
    
    colors = ['white', 'black', 'red', 'green']
    cmap = ListedColormap(colors)
    
    im = ax.imshow(display_maze, cmap=cmap, vmin=0, vmax=3)
    ax.set_title('选项A生成的迷宫', fontsize=16, fontweight='bold')
    ax.set_xticks([])
    ax.set_yticks([])
    
    # 添加网格
    for i in range(22):
        ax.axhline(i - 0.5, color='gray', linewidth=0.5, alpha=0.3)
        ax.axvline(i - 0.5, color='gray', linewidth=0.5, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('option_A_maze.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    visualize_maze()
