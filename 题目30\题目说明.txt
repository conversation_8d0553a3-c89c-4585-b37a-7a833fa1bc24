图中内容：

**1. 中间有四个面相连（1-4-1 型）：共 6 种**

```
1.
   *
 ****
   *

2.
   *
 ****
  *

3.
   *
 ****
 *

4.
  *
 ****
  *

5.
  *
 ****
 *

6.
 *
 ****
 *
```

**2. 中间有三个面相连（3-3 型、3-2-1 型）：共 4 种**

```
7. (3-3 型)
 ***
   ***

8. (3-2-1 型)
 **
  ***
    *

9. (3-2-1 型)
 **
  ***
  *

10. (3-2-1 型)
   *
  ***
 **
```

**3. 中间有两个面相连（2-2-2 型）：共 1 种**

```
11.
 **
  **
   **
```


题目：
图中是立方体的11中展开方式，但是有两个是不独立的，下面哪一段代码可以画出缺少的那种展开方式？

A. 
lines = [
    (3, 1),  
    (1, 4),  
    (3, 1)   
]

for spaces, stars in lines:
    line = ""
    for i in range(spaces):
        line += " "
    for i in range(stars):
        line += "*"
    print(line)

B.
lines = [
    (2, 1),  
    (1, 4),  
    (2, 1)   
]

for spaces, stars in lines:
    line = ""
    for i in range(spaces):
        line += " "
    for i in range(stars):
        line += "*"
    print(line)

C.
rows = [
    [1, 1], 
    [1, 4], 
    [4, 1]
]

for row in rows:
    spaces_count = row[0]
    stars_count = row[1]

    line = ""
    for i in range(spaces_count):
        line += " "
    for j in range(stars_count):
        line += "*"

    print(line)

D.
line_data = [
    (1, 4), 
    (3, 1), 
    (3, 1)
]


for spaces, stars in line_data:
    result = ""
    for space_index in range(spaces):
        result += " "
    for star_index in range(stars):
        result += "*"
    print(result)
