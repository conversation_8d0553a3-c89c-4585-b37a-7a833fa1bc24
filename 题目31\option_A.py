# 选项A - 正确答案
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

plt.rcParams['font.family'] = 'SimHei'
plt.rcParams['axes.unicode_minus'] = False

def complex_loss_function(x, y):
    rosenbrock = 100 * (y - x**2)**2 + (1 - x)**2
    rastrigin = 10 * 2 + (x**2 - 10*np.cos(2*np.pi*x)) + (y**2 - 10*np.cos(2*np.pi*y))
    saddle = 0.5 * (x**2 - y**2) + 0.1 * np.sin(5*x) * np.cos(5*y)
    noise = 0.05 * np.sin(10*x + 7*y) * np.cos(8*x - 6*y)
    loss = 0.2 * rosenbrock + 0.005 * rastrigin + 0.1 * saddle + noise
    global_min_x, global_min_y = 0.5, 0.3
    global_attraction = -2 * np.exp(-((x - global_min_x)**2 + (y - global_min_y)**2) / 0.1)
    return loss + global_attraction

def generate_optimizer_paths():
    sgd_x = np.array([-2.0, -1.8, -1.5, -1.2, -0.8, -0.5, -0.2, 0.1, 0.3, 0.45, 0.5])
    sgd_y = np.array([2.0, 1.7, 1.3, 0.9, 0.6, 0.4, 0.35, 0.32, 0.31, 0.305, 0.3])
    adam_x = np.array([-2.0, -1.6, -1.1, -0.7, -0.3, 0.0, 0.2, 0.35, 0.45, 0.48, 0.5])
    adam_y = np.array([2.0, 1.6, 1.1, 0.8, 0.6, 0.5, 0.4, 0.35, 0.32, 0.31, 0.3])
    rmsprop_x = np.array([-2.0, -1.7, -1.3, -0.9, -0.4, -0.1, 0.15, 0.3, 0.42, 0.48, 0.5])
    rmsprop_y = np.array([2.0, 1.8, 1.4, 1.0, 0.7, 0.5, 0.4, 0.35, 0.32, 0.305, 0.3])
    return (sgd_x, sgd_y), (adam_x, adam_y), (rmsprop_x, rmsprop_y)

x = np.linspace(-3, 3, 100)
y = np.linspace(-2, 3, 100)
X, Y = np.meshgrid(x, y)
Z = complex_loss_function(X, Y)

(sgd_x, sgd_y), (adam_x, adam_y), (rmsprop_x, rmsprop_y) = generate_optimizer_paths()
sgd_z = complex_loss_function(sgd_x, sgd_y)
adam_z = complex_loss_function(adam_x, adam_y)
rmsprop_z = complex_loss_function(rmsprop_x, rmsprop_y)

fig = plt.figure(figsize=(16, 12))

# 3D损失地形图
ax1 = fig.add_subplot(221, projection='3d')
surface = ax1.plot_surface(X, Y, Z, cmap='viridis', alpha=0.7, linewidth=0, antialiased=True)
ax1.plot(sgd_x, sgd_y, sgd_z + 0.1, 'r-o', linewidth=3, markersize=6, label='SGD', alpha=0.9)
ax1.plot(adam_x, adam_y, adam_z + 0.1, 'b-s', linewidth=3, markersize=6, label='Adam', alpha=0.9)
ax1.plot(rmsprop_x, rmsprop_y, rmsprop_z + 0.1, 'g-^', linewidth=3, markersize=6, label='RMSprop', alpha=0.9)
ax1.scatter([-2], [2], [complex_loss_function(-2, 2) + 0.2], color='black', s=100, marker='*', label='起点')
ax1.scatter([0.5], [0.3], [complex_loss_function(0.5, 0.3) + 0.2], color='gold', s=100, marker='*', label='全局最优')
ax1.set_xlabel('参数 θ₁')
ax1.set_ylabel('参数 θ₂')
ax1.set_zlabel('损失函数 L(θ)')
ax1.set_title('神经网络损失地形与优化算法轨迹')
ax1.legend()
ax1.view_init(elev=25, azim=45)

# 等高线图
ax2 = fig.add_subplot(222)
contour = ax2.contour(X, Y, Z, levels=20, colors='gray', alpha=0.6, linewidths=0.8)
contourf = ax2.contourf(X, Y, Z, levels=20, cmap='viridis', alpha=0.8)
ax2.plot(sgd_x, sgd_y, 'r-o', linewidth=3, markersize=8, label='SGD', alpha=0.9)
ax2.plot(adam_x, adam_y, 'b-s', linewidth=3, markersize=8, label='Adam', alpha=0.9)
ax2.plot(rmsprop_x, rmsprop_y, 'g-^', linewidth=3, markersize=8, label='RMSprop', alpha=0.9)
ax2.scatter([-2], [2], color='black', s=150, marker='*', label='起点', zorder=5)
ax2.scatter([0.5], [0.3], color='gold', s=150, marker='*', label='全局最优', zorder=5)
local_minima_x = [-1.5, 1.2, -0.8]
local_minima_y = [1.8, -1.2, -1.5]
ax2.scatter(local_minima_x, local_minima_y, color='red', s=100, marker='x', label='局部最小值', zorder=5)
ax2.set_xlabel('参数 θ₁')
ax2.set_ylabel('参数 θ₂')
ax2.set_title('损失函数等高线图与优化轨迹')
ax2.legend()
ax2.grid(True, alpha=0.3)
plt.colorbar(contourf, ax=ax2, label='损失值')

# 损失收敛曲线
ax3 = fig.add_subplot(223)
epochs = np.arange(len(sgd_z))
ax3.plot(epochs, sgd_z, 'r-o', linewidth=2, markersize=6, label='SGD', alpha=0.8)
ax3.plot(epochs, adam_z, 'b-s', linewidth=2, markersize=6, label='Adam', alpha=0.8)
ax3.plot(epochs, rmsprop_z, 'g-^', linewidth=2, markersize=6, label='RMSprop', alpha=0.8)
ax3.set_xlabel('迭代次数')
ax3.set_ylabel('损失值')
ax3.set_title('优化算法收敛性比较')
ax3.legend()
ax3.grid(True, alpha=0.3)
ax3.set_yscale('log')

# 梯度向量场
ax4 = fig.add_subplot(224)
x_grad = np.linspace(-2.5, 2.5, 15)
y_grad = np.linspace(-1.5, 2.5, 15)
X_grad, Y_grad = np.meshgrid(x_grad, y_grad)
dx = 0.01
dy = 0.01
dZ_dx = (complex_loss_function(X_grad + dx, Y_grad) - complex_loss_function(X_grad - dx, Y_grad)) / (2 * dx)
dZ_dy = (complex_loss_function(X_grad, Y_grad + dy) - complex_loss_function(X_grad, Y_grad - dy)) / (2 * dy)
ax4.quiver(X_grad, Y_grad, -dZ_dx, -dZ_dy, alpha=0.6, scale=50, width=0.003)
contour_bg = ax4.contour(X, Y, Z, levels=15, colors='gray', alpha=0.4, linewidths=0.5)
ax4.plot(sgd_x, sgd_y, 'r-o', linewidth=2, markersize=6, label='SGD', alpha=0.9)
ax4.plot(adam_x, adam_y, 'b-s', linewidth=2, markersize=6, label='Adam', alpha=0.9)
ax4.plot(rmsprop_x, rmsprop_y, 'g-^', linewidth=2, markersize=6, label='RMSprop', alpha=0.9)
ax4.scatter([-2], [2], color='black', s=100, marker='*', label='起点', zorder=5)
ax4.scatter([0.5], [0.3], color='gold', s=100, marker='*', label='全局最优', zorder=5)
ax4.set_xlabel('参数 θ₁')
ax4.set_ylabel('参数 θ₂')
ax4.set_title('梯度向量场与优化路径')
ax4.legend()
ax4.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()
