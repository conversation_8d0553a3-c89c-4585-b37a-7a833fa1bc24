import matplotlib.pyplot as plt
import numpy as np
import random
from matplotlib.colors import ListedColormap

# 设置中文字体
plt.rcParams['font.family'] = 'SimHei'
plt.rcParams['axes.unicode_minus'] = False

def generate_aldous_broder_maze():
    """使用Aldous-Broder算法生成迷宫（错误选项）"""
    width, height = 21, 21
    random.seed(123)
    np.random.seed(123)
    
    maze = np.ones((height, width), dtype=int)
    
    # 计算总的单元格数
    total_cells = ((height - 1) // 2) * ((width - 1) // 2)
    visited_cells = 0
    
    # 随机起始位置
    current_x, current_y = 1, 1
    maze[current_x, current_y] = 0
    visited = set()
    visited.add((current_x, current_y))
    visited_cells = 1
    
    directions = [(-2, 0), (0, 2), (2, 0), (0, -2)]
    
    while visited_cells < total_cells:
        # 随机选择方向
        dx, dy = random.choice(directions)
        next_x, next_y = current_x + dx, current_y + dy
        
        # 检查边界
        if 0 < next_x < height - 1 and 0 < next_y < width - 1:
            if (next_x, next_y) not in visited:
                # 连接两个单元格
                maze[current_x + dx//2, current_y + dy//2] = 0
                maze[next_x, next_y] = 0
                visited.add((next_x, next_y))
                visited_cells += 1
            
            current_x, current_y = next_x, next_y
    
    return maze

def visualize_maze():
    """可视化迷宫"""
    maze = generate_aldous_broder_maze()
    
    fig, ax = plt.subplots(1, 1, figsize=(10, 10))
    
    # 标记起点和终点
    display_maze = maze.copy().astype(float)
    display_maze[1, 1] = 2  # 起点
    display_maze[19, 19] = 3  # 终点
    
    colors = ['white', 'black', 'red', 'green']
    cmap = ListedColormap(colors)
    
    im = ax.imshow(display_maze, cmap=cmap, vmin=0, vmax=3)
    ax.set_title('选项D生成的迷宫', fontsize=16, fontweight='bold')
    ax.set_xticks([])
    ax.set_yticks([])
    
    # 添加网格
    for i in range(22):
        ax.axhline(i - 0.5, color='gray', linewidth=0.5, alpha=0.3)
        ax.axvline(i - 0.5, color='gray', linewidth=0.5, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('option_D_maze.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    visualize_maze()
