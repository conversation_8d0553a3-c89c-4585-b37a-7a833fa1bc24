# 正确答案：使用for循环输出立方体展开图案 1
# 图案：
#    *
#  ****
#    *

# 定义每行的格式：(空格数, 星号数)
pattern_data = [
    (3, 1),  # 第一行：3个空格，1个星号
    (1, 4),  # 第二行：1个空格，4个星号
    (3, 1)   # 第三行：3个空格，1个星号
]

# 使用for循环输出每一行
for spaces_num, stars_num in pattern_data:
    line_output = ""

    # 使用for循环添加空格
    for i in range(spaces_num):
        line_output += " "

    # 使用for循环添加星号
    for j in range(stars_num):
        line_output += "*"

    print(line_output)
