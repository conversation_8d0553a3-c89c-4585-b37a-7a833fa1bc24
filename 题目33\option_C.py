import matplotlib.pyplot as plt
import numpy as np
import random
from matplotlib.colors import ListedColormap

# 设置中文字体
plt.rcParams['font.family'] = 'SimHei'
plt.rcParams['axes.unicode_minus'] = False

def generate_binary_tree_maze():
    """使用二叉树算法生成迷宫（错误选项）"""
    width, height = 21, 21
    random.seed(123)
    np.random.seed(123)
    
    maze = np.ones((height, width), dtype=int)
    
    # 标记所有奇数位置为路径
    for i in range(1, height, 2):
        for j in range(1, width, 2):
            maze[i, j] = 0
    
    # 对每个单元格，随机选择向北或向东连接
    for i in range(1, height, 2):
        for j in range(1, width, 2):
            directions = []
            
            # 可以向北连接
            if i > 1:
                directions.append((-1, 0))
            
            # 可以向东连接
            if j < width - 2:
                directions.append((0, 1))
            
            if directions:
                dx, dy = random.choice(directions)
                maze[i + dx, j + dy] = 0
    
    return maze

def visualize_maze():
    """可视化迷宫"""
    maze = generate_binary_tree_maze()
    
    fig, ax = plt.subplots(1, 1, figsize=(10, 10))
    
    # 标记起点和终点
    display_maze = maze.copy().astype(float)
    display_maze[1, 1] = 2  # 起点
    display_maze[19, 19] = 3  # 终点
    
    colors = ['white', 'black', 'red', 'green']
    cmap = ListedColormap(colors)
    
    im = ax.imshow(display_maze, cmap=cmap, vmin=0, vmax=3)
    ax.set_title('选项C生成的迷宫', fontsize=16, fontweight='bold')
    ax.set_xticks([])
    ax.set_yticks([])
    
    # 添加网格
    for i in range(22):
        ax.axhline(i - 0.5, color='gray', linewidth=0.5, alpha=0.3)
        ax.axvline(i - 0.5, color='gray', linewidth=0.5, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('option_C_maze.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    visualize_maze()
