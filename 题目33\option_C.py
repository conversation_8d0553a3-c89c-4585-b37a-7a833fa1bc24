# 选项C代码 - 对应迷宫C
import numpy as np

def create_maze():
    """创建25x25复杂迷宫"""
    maze = np.ones((25, 25))  # 1表示墙，0表示路径
    
    paths = [
        [(1,1), (1,2), (1,3), (1,4), (1,5), (1,6), (1,7), (1,8), (1,9), (1,10), (1,11), (1,12)],
        [(1,12), (2,12), (3,12), (4,12), (5,12), (6,12), (7,12), (8,12), (9,12)],
        [(9,12), (9,13), (9,14), (9,15), (9,16), (9,17), (9,18), (9,19), (9,20), (9,21), (9,22), (9,23)], 
        [(9,23), (10,23), (11,23), (12,23), (13,23), (14,23), (15,23), (16,23), (17,23), (18,23), (19,23), (20,23), (21,23), (22,23), (23,23)],
        
        [(1,4), (2,4), (3,4), (4,4), (5,4), (6,4), (7,4)], 
        [(3,4), (3,5), (3,6), (3,7), (3,8)],  
        [(5,4), (5,5), (5,6), (5,7), (5,8), (5,9)],  
        [(7,4), (7,5), (7,6), (7,7), (7,8), (7,9), (7,10)],  
        
        [(4,12), (4,13), (4,14), (4,15), (4,16)],  
        [(6,12), (6,11), (6,10), (6,9), (6,8), (6,7)],  
        [(8,12), (8,11), (8,10)],
        
        [(9,16), (10,16), (11,16), (12,16), (13,16)],  
        [(11,16), (11,17), (11,18), (11,19), (11,20), (11,21)],  
        [(13,16), (13,15), (13,14), (13,13), (13,12), (13,11), (13,10)], 
        
        [(14,23), (14,22), (14,21), (14,20), (14,19)],  
        [(16,23), (16,22), (16,21), (16,20), (16,19), (16,18)],  
        [(18,23), (18,22), (18,21), (18,20), (18,19), (18,18)],

        [(1,8), (2,8), (3,8), (4,8)],  
        [(3,8), (4,8), (5,8), (6,8), (7,8), (8,8)], 
        [(8,8), (8,9), (8,10), (8,11), (8,12)],
        [(13,10), (14,10), (15,10), (16,10), (17,10)],
        [(17,10), (17,11), (17,12), (17,13), (17,14)],
        
        [(3,5), (2,5), (2,6)],  
        [(7,10), (8,10), (8,9), (8,8)],  
        [(11,21), (12,21), (12,20), (12,19), (12,18)],  
        [(17,14), (18,14), (19,14), (20,14), (21,14)],  
        [(16,18), (17,18), (18,18)],  
    ]
    
    # 设置路径
    for path in paths:
        for x, y in path:
            if 0 <= x < 25 and 0 <= y < 25:
                maze[x, y] = 0
    
    return maze