# 选项A：正确答案
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.patches import Rectangle
import matplotlib.patches as mpatches

# 设置中文字体
plt.rcParams['font.family'] = 'SimHei'
plt.rcParams['axes.unicode_minus'] = False

# 设置随机种子确保结果可重现
np.random.seed(42)

def generate_attention_weights(seq_len, num_heads, temperature=1.0):
    """生成多头注意力权重矩阵"""
    attention_weights = []
    for head in range(num_heads):
        # 为每个头生成不同的注意力模式
        if head == 0:  # 局部注意力
            weights = np.zeros((seq_len, seq_len))
            for i in range(seq_len):
                for j in range(max(0, i-2), min(seq_len, i+3)):
                    weights[i, j] = np.exp(-abs(i-j) / temperature)
        elif head == 1:  # 全局注意力
            weights = np.random.exponential(0.5, (seq_len, seq_len))
            weights = weights + weights.T  # 对称化
        elif head == 2:  # 位置相关注意力
            weights = np.zeros((seq_len, seq_len))
            for i in range(seq_len):
                for j in range(seq_len):
                    weights[i, j] = np.exp(-((i-j)**2) / (2 * (seq_len/4)**2))
        else:  # 随机注意力
            weights = np.random.gamma(2, 0.3, (seq_len, seq_len))
        
        # 归一化
        weights = weights / weights.sum(axis=1, keepdims=True)
        attention_weights.append(weights)
    
    return np.array(attention_weights)

def create_attention_visualization():
    """创建注意力机制可视化"""
    seq_len = 12
    num_heads = 4
    
    # 生成注意力权重
    attention_weights = generate_attention_weights(seq_len, num_heads)
    
    # 创建图形
    fig = plt.figure(figsize=(16, 12))
    
    # 主标题
    fig.suptitle('多头注意力机制分析：Transformer架构中的注意力权重分布', 
                fontsize=16, fontweight='bold', y=0.95)
    
    # 创建网格布局
    gs = fig.add_gridspec(3, 4, height_ratios=[1, 1, 0.8], width_ratios=[1, 1, 1, 1],
                         hspace=0.3, wspace=0.3)
    
    # 绘制各个注意力头的热力图
    head_names = ['局部注意力头', '全局注意力头', '位置注意力头', '随机注意力头']
    
    for i in range(num_heads):
        ax = fig.add_subplot(gs[0, i])
        im = ax.imshow(attention_weights[i], cmap='YlOrRd', aspect='auto')
        ax.set_title(f'{head_names[i]}\n(Head {i+1})', fontsize=11, fontweight='bold')
        ax.set_xlabel('Key位置')
        ax.set_ylabel('Query位置')
        
        # 添加数值标注（仅显示部分以避免过于密集）
        for row in range(0, seq_len, 3):
            for col in range(0, seq_len, 3):
                text = ax.text(col, row, f'{attention_weights[i, row, col]:.2f}',
                             ha="center", va="center", color="black", fontsize=8)
        
        plt.colorbar(im, ax=ax, shrink=0.8)
    
    # 计算平均注意力权重
    avg_attention = np.mean(attention_weights, axis=0)
    
    # 绘制平均注意力热力图
    ax_avg = fig.add_subplot(gs[1, :2])
    im_avg = ax_avg.imshow(avg_attention, cmap='viridis', aspect='auto')
    ax_avg.set_title('多头平均注意力权重矩阵', fontsize=12, fontweight='bold')
    ax_avg.set_xlabel('Key位置')
    ax_avg.set_ylabel('Query位置')
    plt.colorbar(im_avg, ax=ax_avg, shrink=0.6)
    
    # 绘制注意力权重分布直方图
    ax_hist = fig.add_subplot(gs[1, 2:])
    colors = ['red', 'blue', 'green', 'orange']
    for i in range(num_heads):
        weights_flat = attention_weights[i].flatten()
        ax_hist.hist(weights_flat, bins=30, alpha=0.6, label=f'Head {i+1}', 
                    color=colors[i], density=True)
    
    ax_hist.set_title('各注意力头权重分布', fontsize=12, fontweight='bold')
    ax_hist.set_xlabel('注意力权重值')
    ax_hist.set_ylabel('密度')
    ax_hist.legend()
    ax_hist.grid(True, alpha=0.3)
    
    # 绘制注意力模式分析
    ax_pattern = fig.add_subplot(gs[2, :])
    
    # 计算每个位置的注意力集中度（熵）
    attention_entropy = []
    for i in range(seq_len):
        entropy_per_head = []
        for head in range(num_heads):
            weights = attention_weights[head, i, :]
            entropy = -np.sum(weights * np.log(weights + 1e-10))
            entropy_per_head.append(entropy)
        attention_entropy.append(entropy_per_head)
    
    attention_entropy = np.array(attention_entropy)
    
    # 绘制注意力熵分析
    x_pos = np.arange(seq_len)
    width = 0.2
    
    for head in range(num_heads):
        ax_pattern.bar(x_pos + head * width, attention_entropy[:, head], 
                      width, label=f'Head {head+1}', color=colors[head], alpha=0.7)
    
    ax_pattern.set_title('各位置注意力集中度分析（熵值越低表示注意力越集中）', 
                        fontsize=12, fontweight='bold')
    ax_pattern.set_xlabel('序列位置')
    ax_pattern.set_ylabel('注意力熵值')
    ax_pattern.set_xticks(x_pos + width * 1.5)
    ax_pattern.set_xticklabels([f'Pos{i}' for i in range(seq_len)])
    ax_pattern.legend()
    ax_pattern.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    create_attention_visualization()
