import matplotlib.pyplot as plt
import numpy as np
import random
from matplotlib.colors import ListedColormap

# 设置中文字体
plt.rcParams['font.family'] = 'SimHei'
plt.rcParams['axes.unicode_minus'] = False

def generate_target_maze():
    """生成目标迷宫 - 使用递归回溯算法的特定变体（正确答案）"""
    width, height = 21, 21
    random.seed(123)
    np.random.seed(123)
    
    maze = np.ones((height, width), dtype=int)
    directions = [(-2, 0), (0, 2), (2, 0), (0, -2)]
    
    def is_valid(x, y):
        return 0 <= x < height and 0 <= y < width
    
    def carve_path(x, y):
        maze[x, y] = 0
        dir_scores = []
        for dx, dy in directions:
            nx, ny = x + dx, y + dy
            if is_valid(nx, ny) and maze[nx, ny] == 1:
                score = abs(nx - height//2) + abs(ny - width//2)
                dir_scores.append((score, dx, dy))
        
        dir_scores.sort(reverse=True)
        
        for score, dx, dy in dir_scores:
            nx, ny = x + dx, y + dy
            if is_valid(nx, ny) and maze[nx, ny] == 1:
                maze[x + dx//2, y + dy//2] = 0
                carve_path(nx, ny)
    
    carve_path(1, 1)
    
    special_connections = [(3, 5), (7, 9), (11, 13), (15, 17)]
    for x, y in special_connections:
        if maze[x, y] == 1 and (maze[x-1, y] == 0 or maze[x+1, y] == 0 or 
                                maze[x, y-1] == 0 or maze[x, y+1] == 0):
            maze[x, y] = 0
    
    return maze

def generate_prim_maze():
    """使用Prim算法生成迷宫"""
    width, height = 21, 21
    random.seed(123)
    np.random.seed(123)
    
    maze = np.ones((height, width), dtype=int)
    start_x, start_y = 1, 1
    maze[start_x, start_y] = 0
    walls = []
    
    def add_walls(x, y):
        for dx, dy in [(-2, 0), (0, 2), (2, 0), (0, -2)]:
            nx, ny = x + dx, y + dy
            if 0 <= nx < height and 0 <= ny < width and maze[nx, ny] == 1:
                walls.append((x + dx//2, y + dy//2, nx, ny))
    
    add_walls(start_x, start_y)
    
    while walls:
        wall_idx = random.randint(0, len(walls) - 1)
        wall_x, wall_y, cell_x, cell_y = walls.pop(wall_idx)
        
        if maze[cell_x, cell_y] == 1:
            maze[wall_x, wall_y] = 0
            maze[cell_x, cell_y] = 0
            add_walls(cell_x, cell_y)
    
    return maze

def generate_binary_tree_maze():
    """使用二叉树算法生成迷宫"""
    width, height = 21, 21
    random.seed(123)
    np.random.seed(123)
    
    maze = np.ones((height, width), dtype=int)
    
    for i in range(1, height, 2):
        for j in range(1, width, 2):
            maze[i, j] = 0
    
    for i in range(1, height, 2):
        for j in range(1, width, 2):
            directions = []
            if i > 1:
                directions.append((-1, 0))
            if j < width - 2:
                directions.append((0, 1))
            
            if directions:
                dx, dy = random.choice(directions)
                maze[i + dx, j + dy] = 0
    
    return maze

def generate_aldous_broder_maze():
    """使用Aldous-Broder算法生成迷宫"""
    width, height = 21, 21
    random.seed(123)
    np.random.seed(123)
    
    maze = np.ones((height, width), dtype=int)
    total_cells = ((height - 1) // 2) * ((width - 1) // 2)
    visited_cells = 0
    
    current_x, current_y = 1, 1
    maze[current_x, current_y] = 0
    visited = set()
    visited.add((current_x, current_y))
    visited_cells = 1
    
    directions = [(-2, 0), (0, 2), (2, 0), (0, -2)]
    
    while visited_cells < total_cells:
        dx, dy = random.choice(directions)
        next_x, next_y = current_x + dx, current_y + dy
        
        if 0 < next_x < height - 1 and 0 < next_y < width - 1:
            if (next_x, next_y) not in visited:
                maze[current_x + dx//2, current_y + dy//2] = 0
                maze[next_x, next_y] = 0
                visited.add((next_x, next_y))
                visited_cells += 1
            
            current_x, current_y = next_x, next_y
    
    return maze

def visualize_all_mazes():
    """可视化所有迷宫进行对比"""
    # 生成所有迷宫
    target_maze = generate_target_maze()
    prim_maze = generate_prim_maze()
    binary_maze = generate_binary_tree_maze()
    aldous_maze = generate_aldous_broder_maze()
    
    # 创建大图显示目标迷宫
    fig1, ax1 = plt.subplots(1, 1, figsize=(12, 12))
    
    display_maze = target_maze.copy().astype(float)
    display_maze[1, 1] = 2  # 起点
    display_maze[19, 19] = 3  # 终点
    
    colors = ['white', 'black', 'red', 'green']
    cmap = ListedColormap(colors)
    
    im1 = ax1.imshow(display_maze, cmap=cmap, vmin=0, vmax=3)
    ax1.set_title('目标迷宫\n请判断以下哪个代码选项可以生成此迷宫模式', fontsize=18, fontweight='bold', pad=20)
    ax1.set_xticks([])
    ax1.set_yticks([])
    
    # 添加网格
    for i in range(22):
        ax1.axhline(i - 0.5, color='gray', linewidth=0.5, alpha=0.3)
        ax1.axvline(i - 0.5, color='gray', linewidth=0.5, alpha=0.3)
    
    # 添加特征说明
    textstr = '特征观察要点：\n• 通道的分布模式\n• 分支的复杂程度\n• 死胡同的数量\n• 整体的连通性'
    props = dict(boxstyle='round', facecolor='lightblue', alpha=0.8)
    ax1.text(0.02, 0.98, textstr, transform=ax1.transAxes, fontsize=12,
            verticalalignment='top', bbox=props)
    
    plt.tight_layout()
    plt.savefig('C:/Users/<USER>/OneDrive/Desktop/VLM prompt/VLM-code/code/题目33/target_maze.png', 
                dpi=300, bbox_inches='tight')
    plt.show()
    
    # 创建四个选项的对比图
    fig2, axes = plt.subplots(2, 2, figsize=(16, 16))
    fig2.suptitle('四个代码选项生成的迷宫对比', fontsize=20, fontweight='bold')
    
    mazes = [
        (target_maze, '选项A：改进递归回溯算法', 'A'),
        (prim_maze, '选项B：随机Prim算法', 'B'),
        (binary_maze, '选项C：二叉树算法', 'C'),
        (aldous_maze, '选项D：Aldous-Broder算法', 'D')
    ]
    
    for idx, (maze, title, option) in enumerate(mazes):
        ax = axes[idx // 2, idx % 2]
        
        display_maze = maze.copy().astype(float)
        display_maze[1, 1] = 2  # 起点
        display_maze[19, 19] = 3  # 终点
        
        im = ax.imshow(display_maze, cmap=cmap, vmin=0, vmax=3)
        ax.set_title(title, fontsize=14, fontweight='bold', pad=15)
        ax.set_xticks([])
        ax.set_yticks([])
        
        # 添加网格
        for i in range(22):
            ax.axhline(i - 0.5, color='gray', linewidth=0.5, alpha=0.3)
            ax.axvline(i - 0.5, color='gray', linewidth=0.5, alpha=0.3)
        
        # 添加选项标识
        ax.text(0.05, 0.95, f'选项{option}', transform=ax.transAxes, fontsize=16,
                fontweight='bold', bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.8))
        
        # 计算迷宫特征
        path_count = np.sum(maze == 0)
        total_cells = maze.shape[0] * maze.shape[1]
        density = path_count / total_cells
        
        feature_text = f'路径密度: {density:.2f}\n路径数: {path_count}'
        ax.text(0.05, 0.05, feature_text, transform=ax.transAxes, fontsize=10,
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('C:/Users/<USER>/OneDrive/Desktop/VLM prompt/VLM-code/code/题目33/maze_options_comparison.png', 
                dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    visualize_all_mazes()
