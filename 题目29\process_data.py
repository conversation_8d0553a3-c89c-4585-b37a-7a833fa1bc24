import pandas as pd
import re
import os
from pandas.tseries.offsets import Week

def get_contract_series(code):
    """
    从期权代码中提取合约系列。
    """
    code_str = str(code)
    match = re.search(r'SC(\d{4})', code_str)
    if match:
        return match.group(1)
    return None

def get_option_type(code):
    """
    根据期权代码的倒数第四位判断是认购期权（'C'）还是认沽期权（'P'）。
    """
    code_str = str(code)
    if len(code_str) >= 4:
        type_char = code_str[-4]
        if type_char in ['C', 'P']:
            return type_char
    return None

def load_and_preprocess_history(filepath='原油期权行情历史.csv'):
    """
    加载并预处理行情历史数据。
    """
    try:
        df = pd.read_csv(filepath, encoding='gbk')
    except UnicodeDecodeError:
        df = pd.read_csv(filepath, encoding='utf-8')

    df['期权代码'] = df['期权代码'].astype(str)
    df['期权类型'] = df['期权代码'].apply(get_option_type)
    df['合约系列'] = df['期权代码'].apply(get_contract_series)
    
    if '成交额（万）' in df.columns:
        df['成交额（万）'] = pd.to_numeric(df['成交额（万）'], errors='coerce').fillna(0)
    
    if '日期' in df.columns:
        df['日期'] = pd.to_datetime(df['日期'])

    return df

def get_custom_week_period(date):
    """
    周度划分函数。
    """
    return date - pd.to_timedelta(date.weekday(), unit='d') + pd.to_timedelta(6, unit='d')

def get_data_subset(demand_row, history_df):
    """
    从行情历史中筛选出相应的数据范围。
    """
    data_sample_type = demand_row['数据样本']
    update_frequency = demand_row['更新频率']
    base_df = history_df.copy()

    if data_sample_type == '品种认购+认沽最高成交额合约合计':
        all_top_contracts_data = []
        for opt_type in ['C', 'P']:
            options_df = base_df[base_df['期权类型'] == opt_type].copy()
            if options_df.empty: continue
            
            if update_frequency == '日度': 
                options_df['time_period'] = options_df['日期']
            elif update_frequency == '周度平均': 
                options_df['time_period'] = options_df['日期'].apply(get_custom_week_period)
            else: 
                options_df['time_period'] = options_df['日期'].dt.to_period('M').apply(lambda p: p.end_time).dt.normalize()
            
            turnover = options_df.groupby(['time_period', '合约系列'])['成交额（万）'].sum().reset_index()
            if turnover.empty: 
                continue
            idx = turnover.groupby('time_period')['成交额（万）'].idxmax()
            top_contracts = turnover.loc[idx][['time_period', '合约系列']]
            result = pd.merge(options_df, top_contracts, on=['time_period', '合约系列'])
            all_top_contracts_data.append(result)
        
        if not all_top_contracts_data: 
            return pd.DataFrame()
        return pd.concat(all_top_contracts_data).drop(columns=['time_period'], errors='ignore')

    if '认购合约' in data_sample_type: 
        base_df = base_df[base_df['期权类型'] == 'C']
    elif '认沽合约' in data_sample_type: 
        base_df = base_df[base_df['期权类型'] == 'P']
    
    if '最高成交额' not in data_sample_type: 
        return base_df
    if base_df.empty: 
        return base_df

    if update_frequency == '日度': 
        base_df['time_period'] = base_df['日期']
    elif update_frequency == '周度平均': 
        base_df['time_period'] = base_df['日期'].apply(get_custom_week_period)
    else: 
        base_df['time_period'] = base_df['日期'].dt.to_period('M').apply(lambda p: p.end_time).dt.normalize()
    
    turnover = base_df.groupby(['time_period', '合约系列'])['成交额（万）'].sum().reset_index()
    if turnover.empty: 
        return pd.DataFrame()
    
    idx = turnover.groupby('time_period')['成交额（万）'].idxmax()
    top_contracts = turnover.loc[idx][['time_period', '合约系列']]
    result_df = pd.merge(base_df, top_contracts, on=['time_period', '合约系列'])
    return result_df.drop(columns=['time_period'], errors='ignore')

def get_required_metrics(demand_row):
    """
    从需求行的 '包含指标' 列中提取出所有非空的指标名称。
    """
    metric_cols = [col for col in demand_row.index if '包含指标' in col]
    return demand_row[metric_cols].dropna().tolist()

def calculate_final_aggregation(metrics_df, demand_row):
    """
    对筛选好的数据进行最终的聚合计算。
    """
    if metrics_df.empty: 
        return None
    
    aggregation_type = demand_row['分类']
    update_frequency = demand_row['更新频率']
    required_metrics = get_required_metrics(demand_row)

    for metric in required_metrics:
        if metric in metrics_df.columns:
            metrics_df[metric] = pd.to_numeric(metrics_df[metric], errors='coerce').fillna(0)
        else:
            metrics_df[metric] = 0

    agg_funcs = {}
    if aggregation_type == '日期连续合计值':
        agg_funcs = {metric: 'sum' for metric in required_metrics}
    elif aggregation_type == '日期连续算数平均值':
        agg_funcs = {metric: 'mean' for metric in required_metrics}
    elif aggregation_type == '日期连续加权平均值':
        def weighted_average(x, weights_col='成交额（万）'):
            weights = metrics_df.loc[x.index, weights_col]
            return 0 if weights.sum() == 0 else (x * weights).sum() / weights.sum()
        agg_funcs = {metric: weighted_average for metric in required_metrics}

    daily_aggregated = metrics_df.groupby('日期').agg(agg_funcs).reset_index()
    if daily_aggregated.empty: return None

    if update_frequency == '日度':
        return daily_aggregated
    else:
        if update_frequency == '周度平均':
            daily_aggregated['time_period'] = daily_aggregated['日期'].apply(get_custom_week_period)
        else:
            daily_aggregated['time_period'] = daily_aggregated['日期'].dt.to_period('M').apply(lambda p: p.end_time).dt.normalize()
        
        final_result = daily_aggregated.groupby('time_period')[required_metrics].mean().reset_index()
        return final_result.rename(columns={'time_period': '日期'})

def main():
    """
    主执行函数：加载数据，遍历需求，计算并为每个需求生成一个结果文件，存入分类的文件夹中。
    """
    print("开始执行数据处理任务...")
    
    output_dirs = {'日度': '日度', '周度平均': '周度平均', '月度平均': '月度平均'}
    for dir_path in output_dirs.values():
        os.makedirs(dir_path, exist_ok=True)
        
    try:
        df_demands = pd.read_csv('原油期权数据需求.csv', encoding='utf-8')
        df_history = load_and_preprocess_history('原油期权行情历史.csv')
    except FileNotFoundError as e:
        print(f"错误：无法找到必要的数据文件: {e}")
        return

    print(f"共找到 {len(df_demands)} 条处理需求。")

    for index, demand_row in df_demands.iterrows():
        demand_id = demand_row['需求序列']
        print(f"\n处理中... 需求序列: {demand_id} ({index + 1}/{len(df_demands)})")

        filtered_data = get_data_subset(demand_row, df_history)
        if filtered_data.empty:
            print("  - 筛选结果为空，跳过此需求。")
            continue
        print(f"  - 筛选出 {len(filtered_data)} 行数据。")

        result_df = calculate_final_aggregation(filtered_data, demand_row)
        
        if result_df is None or result_df.empty:
            print("  - 聚合结果为空，跳过。")
            continue
        
        # 添加元数据列
        result_df['需求序列'] = demand_id

        # 整理列顺序
        metric_cols = get_required_metrics(demand_row)
        final_cols = ['需求序列', '日期']

        freq = demand_row['更新频率']
        temp_data = filtered_data.copy()

        if freq == '周度平均':
            temp_data['time_period'] = temp_data['日期'].apply(get_custom_week_period)
        elif freq == '月度平均':
            temp_data['time_period'] = temp_data['日期'].dt.to_period('M').apply(lambda p: p.end_time).dt.normalize()
        else:
            temp_data['time_period'] = temp_data['日期']

        code_map = temp_data.groupby('time_period')['期权代码'].apply(
            lambda s: ",".join(s.str[:6].unique())
        ).rename('期权代码')

        result_df = pd.merge(result_df, code_map, left_on='日期', right_index=True, how='left')
        final_cols.append('期权代码')
        
        final_cols.extend([m for m in metric_cols if m in result_df.columns])
        result_df = result_df.reindex(columns=final_cols)

        # 所有指标列保留两位小数
        # for col in metric_cols:
        #     if col in result_df.columns:
        #         result_df[col] = result_df[col].round(2)

        # 确定输出路径并保存
        freq = demand_row['更新频率']
        output_dir = output_dirs.get(freq)
        if output_dir:
            filename = f"{demand_row['分类']}_{demand_row['数据样本']}.csv"
            filepath = os.path.join(output_dir, filename)
            result_df.to_csv(filepath, index=False, encoding='utf-8-sig')
            print(f"  - 计算完成，结果已保存至: {filepath}")
        else:
            print(f"  - 警告: 未知的更新频率 '{freq}'，无法保存文件。")

    print("\n所有任务执行完毕！")

if __name__ == '__main__':
    main()
