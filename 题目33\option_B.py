# 选项B代码 - 对应迷宫B
import numpy as np

def create_maze():
    """创建25x25复杂迷宫"""
    maze = np.ones((25, 25))  # 1表示墙，0表示路径
    
    paths = [
        [(1,1), (1,2), (1,3), (1,4), (1,5), (1,6), (1,7), (1,8), (1,9), (1,10), (1,11), (1,12)],
        [(1,12), (2,12), (3,12), (4,12), (5,12), (6,12), (7,12), (8,12), (9,12), (10,12)],
        [(10,12), (10,13), (10,14), (10,15), (10,16), (10,17), (10,18), (10,19), (10,20), (10,21), (10,22), (10,23)],
        [(10,23), (11,23), (12,23), (13,23), (14,23), (15,23), (16,23), (17,23), (18,23), (19,23), (20,23), (21,23), (22,23), (23,23)],
        
        [(1,4), (2,4), (3,4), (4,4), (5,4), (6,4), (7,4), (8,4)],
        [(3,4), (3,5), (3,6), (3,7), (3,8), (3,9), (3,10)],
        [(5,4), (5,5), (5,6), (5,7), (5,8)],  
        [(7,4), (7,5), (7,6), (7,7)],  
        
        # 分支路径2 - 中上区域
        [(4,12), (4,13), (4,14), (4,15), (4,16), (4,17)],
        [(6,12), (6,11), (6,10), (6,9), (6,8), (6,7), (6,6), (6,5)],
        [(8,12), (8,11), (8,10), (8,9), (8,8)],
        
        # 分支路径3 - 右侧区域
        [(10,16), (11,16), (12,16), (13,16), (14,16), (15,16), (16,16)],
        [(12,16), (12,17), (12,18), (12,19), (12,20), (12,21)],
        [(14,16), (14,15), (14,14), (14,13), (14,12)],
        
        # 分支路径4 - 下方区域
        [(15,23), (15,22), (15,21), (15,20), (15,19), (15,18)],
        [(17,23), (17,22), (17,21), (17,20), (17,19)], 
        [(19,23), (19,22), (19,21), (19,20)],  
        
        # 额外复杂路径
        [(1,8), (2,8), (3,8), (4,8), (5,8)],
        [(3,10), (4,10), (5,10), (6,10), (7,10), (8,10), (9,10)],  
        [(9,10), (9,11), (9,12)],
        [(14,12), (15,12), (16,12), (17,12), (18,12)],
        [(18,12), (18,13), (18,14), (18,15)],
        
        # 死胡同
        [(3,7), (2,7), (2,6)], 
        [(8,8), (9,8), (9,7), (9,6), (9,5)], 
        [(12,21), (13,21), (13,20)],  
        [(18,15), (19,15), (20,15)],  
        [(15,18), (16,18), (17,18), (18,18), (19,18)], 
    ]
    
    # 设置路径
    for path in paths:
        for x, y in path:
            if 0 <= x < 25 and 0 <= y < 25:
                maze[x, y] = 0
    
    return maze
