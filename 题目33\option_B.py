import matplotlib.pyplot as plt
import numpy as np
import random
from matplotlib.colors import ListedColormap

# 设置中文字体
plt.rcParams['font.family'] = 'SimHei'
plt.rcParams['axes.unicode_minus'] = False

def generate_prim_maze():
    """使用Prim算法生成迷宫（错误选项）"""
    width, height = 21, 21
    random.seed(123)
    np.random.seed(123)
    
    maze = np.ones((height, width), dtype=int)
    
    # 从随机位置开始
    start_x, start_y = 1, 1
    maze[start_x, start_y] = 0
    
    # 墙列表
    walls = []
    
    def add_walls(x, y):
        for dx, dy in [(-2, 0), (0, 2), (2, 0), (0, -2)]:
            nx, ny = x + dx, y + dy
            if 0 <= nx < height and 0 <= ny < width and maze[nx, ny] == 1:
                walls.append((x + dx//2, y + dy//2, nx, ny))
    
    add_walls(start_x, start_y)
    
    while walls:
        # 随机选择一面墙
        wall_idx = random.randint(0, len(walls) - 1)
        wall_x, wall_y, cell_x, cell_y = walls.pop(wall_idx)
        
        if maze[cell_x, cell_y] == 1:
            maze[wall_x, wall_y] = 0  # 打通墙
            maze[cell_x, cell_y] = 0  # 标记单元格
            add_walls(cell_x, cell_y)
    
    return maze

def visualize_maze():
    """可视化迷宫"""
    maze = generate_prim_maze()
    
    fig, ax = plt.subplots(1, 1, figsize=(10, 10))
    
    # 标记起点和终点
    display_maze = maze.copy().astype(float)
    display_maze[1, 1] = 2  # 起点
    display_maze[19, 19] = 3  # 终点
    
    colors = ['white', 'black', 'red', 'green']
    cmap = ListedColormap(colors)
    
    im = ax.imshow(display_maze, cmap=cmap, vmin=0, vmax=3)
    ax.set_title('选项B生成的迷宫', fontsize=16, fontweight='bold')
    ax.set_xticks([])
    ax.set_yticks([])
    
    # 添加网格
    for i in range(22):
        ax.axhline(i - 0.5, color='gray', linewidth=0.5, alpha=0.3)
        ax.axvline(i - 0.5, color='gray', linewidth=0.5, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('option_B_maze.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    visualize_maze()
