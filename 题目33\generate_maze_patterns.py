import matplotlib.pyplot as plt
import numpy as np
import random
from matplotlib.colors import ListedColormap
import matplotlib.patches as patches

# 设置中文字体
plt.rcParams['font.family'] = 'SimHei'
plt.rcParams['axes.unicode_minus'] = False

def generate_recursive_backtrack_maze(width, height, seed=42):
    """使用递归回溯算法生成迷宫"""
    random.seed(seed)
    np.random.seed(seed)
    
    # 初始化迷宫，全部为墙
    maze = np.ones((height, width), dtype=int)
    
    # 方向：上、右、下、左
    directions = [(-2, 0), (0, 2), (2, 0), (0, -2)]
    
    def is_valid(x, y):
        return 0 <= x < height and 0 <= y < width
    
    def carve_path(x, y):
        maze[x, y] = 0  # 标记为路径
        
        # 随机打乱方向
        dirs = directions.copy()
        random.shuffle(dirs)
        
        for dx, dy in dirs:
            nx, ny = x + dx, y + dy
            if is_valid(nx, ny) and maze[nx, ny] == 1:
                # 打通墙壁
                maze[x + dx//2, y + dy//2] = 0
                carve_path(nx, ny)
    
    # 从(1,1)开始生成
    carve_path(1, 1)
    
    # 确保起点和终点
    maze[1, 1] = 0  # 起点
    maze[height-2, width-2] = 0  # 终点
    
    return maze

def generate_prim_maze(width, height, seed=42):
    """使用Prim算法生成迷宫"""
    random.seed(seed)
    np.random.seed(seed)
    
    maze = np.ones((height, width), dtype=int)
    
    # 从随机位置开始
    start_x, start_y = 1, 1
    maze[start_x, start_y] = 0
    
    # 墙列表
    walls = []
    
    def add_walls(x, y):
        for dx, dy in [(-2, 0), (0, 2), (2, 0), (0, -2)]:
            nx, ny = x + dx, y + dy
            if 0 <= nx < height and 0 <= ny < width and maze[nx, ny] == 1:
                walls.append((x + dx//2, y + dy//2, nx, ny))
    
    add_walls(start_x, start_y)
    
    while walls:
        # 随机选择一面墙
        wall_idx = random.randint(0, len(walls) - 1)
        wall_x, wall_y, cell_x, cell_y = walls.pop(wall_idx)
        
        if maze[cell_x, cell_y] == 1:
            maze[wall_x, wall_y] = 0  # 打通墙
            maze[cell_x, cell_y] = 0  # 标记单元格
            add_walls(cell_x, cell_y)
    
    return maze

def generate_binary_tree_maze(width, height, seed=42):
    """使用二叉树算法生成迷宫"""
    random.seed(seed)
    np.random.seed(seed)
    
    maze = np.ones((height, width), dtype=int)
    
    # 标记所有奇数位置为路径
    for i in range(1, height, 2):
        for j in range(1, width, 2):
            maze[i, j] = 0
    
    # 对每个单元格，随机选择向北或向东连接
    for i in range(1, height, 2):
        for j in range(1, width, 2):
            directions = []
            
            # 可以向北连接
            if i > 1:
                directions.append((-1, 0))
            
            # 可以向东连接
            if j < width - 2:
                directions.append((0, 1))
            
            if directions:
                dx, dy = random.choice(directions)
                maze[i + dx, j + dy] = 0
    
    return maze

def generate_aldous_broder_maze(width, height, seed=42):
    """使用Aldous-Broder算法生成迷宫"""
    random.seed(seed)
    np.random.seed(seed)
    
    maze = np.ones((height, width), dtype=int)
    
    # 计算总的单元格数
    total_cells = ((height - 1) // 2) * ((width - 1) // 2)
    visited_cells = 0
    
    # 随机起始位置
    current_x, current_y = 1, 1
    maze[current_x, current_y] = 0
    visited = set()
    visited.add((current_x, current_y))
    visited_cells = 1
    
    directions = [(-2, 0), (0, 2), (2, 0), (0, -2)]
    
    while visited_cells < total_cells:
        # 随机选择方向
        dx, dy = random.choice(directions)
        next_x, next_y = current_x + dx, current_y + dy
        
        # 检查边界
        if 0 < next_x < height - 1 and 0 < next_y < width - 1:
            if (next_x, next_y) not in visited:
                # 连接两个单元格
                maze[current_x + dx//2, current_y + dy//2] = 0
                maze[next_x, next_y] = 0
                visited.add((next_x, next_y))
                visited_cells += 1
            
            current_x, current_y = next_x, next_y
    
    return maze

def visualize_maze_comparison():
    """可视化四种不同的迷宫生成算法"""
    width, height = 21, 21
    
    # 生成四种不同的迷宫
    maze1 = generate_recursive_backtrack_maze(width, height, seed=42)
    maze2 = generate_prim_maze(width, height, seed=42)
    maze3 = generate_binary_tree_maze(width, height, seed=42)
    maze4 = generate_aldous_broder_maze(width, height, seed=42)
    
    # 创建图形
    fig, axes = plt.subplots(2, 2, figsize=(16, 16))
    fig.suptitle('四种迷宫生成算法对比', fontsize=20, fontweight='bold')
    
    # 定义颜色：白色(路径)，黑色(墙)，红色(起点)，绿色(终点)
    colors = ['white', 'black', 'red', 'green']
    cmap = ListedColormap(colors)
    
    mazes = [
        (maze1, '递归回溯算法\n(Recursive Backtracking)', '特点：生成长而曲折的通道，分支较少'),
        (maze2, 'Prim算法\n(Randomized Prim)', '特点：生成较多短分支，整体较为密集'),
        (maze3, '二叉树算法\n(Binary Tree)', '特点：右下角总有通路，具有明显的偏向性'),
        (maze4, 'Aldous-Broder算法\n(Aldous-Broder)', '特点：生成均匀分布的迷宫，无明显偏向')
    ]
    
    for idx, (maze, title, description) in enumerate(mazes):
        ax = axes[idx // 2, idx % 2]
        
        # 标记起点和终点
        display_maze = maze.copy().astype(float)
        display_maze[1, 1] = 2  # 起点
        display_maze[height-2, width-2] = 3  # 终点
        
        im = ax.imshow(display_maze, cmap=cmap, vmin=0, vmax=3)
        ax.set_title(f'{title}\n{description}', fontsize=12, fontweight='bold', pad=20)
        ax.set_xticks([])
        ax.set_yticks([])
        
        # 添加网格
        for i in range(height + 1):
            ax.axhline(i - 0.5, color='gray', linewidth=0.5, alpha=0.3)
        for j in range(width + 1):
            ax.axvline(j - 0.5, color='gray', linewidth=0.5, alpha=0.3)
        
        # 添加算法特征说明
        textstr = f'尺寸: {width}×{height}\n路径密度: {np.sum(maze == 0)}/{width*height}\n连通性: 完全连通'
        props = dict(boxstyle='round', facecolor='wheat', alpha=0.8)
        ax.text(0.02, 0.98, textstr, transform=ax.transAxes, fontsize=9,
                verticalalignment='top', bbox=props)
    
    plt.tight_layout()
    plt.savefig('C:/Users/<USER>/OneDrive/Desktop/VLM prompt/VLM-code/code/题目33/maze_generation_algorithms_comparison.png', 
                dpi=300, bbox_inches='tight')
    plt.show()

def generate_target_maze():
    """生成目标迷宫 - 使用递归回溯算法的特定变体"""
    width, height = 21, 21
    random.seed(123)  # 使用特定种子
    np.random.seed(123)
    
    maze = np.ones((height, width), dtype=int)
    
    # 递归回溯的特殊变体：优先选择最长路径
    directions = [(-2, 0), (0, 2), (2, 0), (0, -2)]
    
    def is_valid(x, y):
        return 0 <= x < height and 0 <= y < width
    
    def carve_path(x, y):
        maze[x, y] = 0
        
        # 计算每个方向的潜在路径长度，优先选择能创造更长路径的方向
        dir_scores = []
        for dx, dy in directions:
            nx, ny = x + dx, y + dy
            if is_valid(nx, ny) and maze[nx, ny] == 1:
                # 计算这个方向的"深度"得分
                score = abs(nx - height//2) + abs(ny - width//2)  # 距离中心的距离
                dir_scores.append((score, dx, dy))
        
        # 按得分排序，优先选择得分高的方向
        dir_scores.sort(reverse=True)
        
        for score, dx, dy in dir_scores:
            nx, ny = x + dx, y + dy
            if is_valid(nx, ny) and maze[nx, ny] == 1:
                maze[x + dx//2, y + dy//2] = 0
                carve_path(nx, ny)
    
    carve_path(1, 1)
    
    # 添加一些特殊的连接以增加复杂性
    special_connections = [(3, 5), (7, 9), (11, 13), (15, 17)]
    for x, y in special_connections:
        if maze[x, y] == 1 and (maze[x-1, y] == 0 or maze[x+1, y] == 0 or 
                                maze[x, y-1] == 0 or maze[x, y+1] == 0):
            maze[x, y] = 0
    
    return maze

def visualize_target_maze():
    """可视化目标迷宫"""
    maze = generate_target_maze()
    
    fig, ax = plt.subplots(1, 1, figsize=(10, 10))
    
    # 标记起点和终点
    display_maze = maze.copy().astype(float)
    display_maze[1, 1] = 2  # 起点
    display_maze[19, 19] = 3  # 终点
    
    colors = ['white', 'black', 'red', 'green']
    cmap = ListedColormap(colors)
    
    im = ax.imshow(display_maze, cmap=cmap, vmin=0, vmax=3)
    ax.set_title('目标迷宫\n请判断哪个代码选项可以生成此迷宫', fontsize=16, fontweight='bold', pad=20)
    ax.set_xticks([])
    ax.set_yticks([])
    
    # 添加网格
    for i in range(22):
        ax.axhline(i - 0.5, color='gray', linewidth=0.5, alpha=0.3)
        ax.axvline(i - 0.5, color='gray', linewidth=0.5, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('C:/Users/<USER>/OneDrive/Desktop/VLM prompt/VLM-code/code/题目33/target_maze.png', 
                dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    # 生成对比图
    visualize_maze_comparison()
    # 生成目标迷宫
    visualize_target_maze()
