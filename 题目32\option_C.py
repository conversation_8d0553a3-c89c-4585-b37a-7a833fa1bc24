import matplotlib.pyplot as plt
import numpy as np
from collections import deque
import matplotlib.patches as patches
from matplotlib.colors import ListedColormap
import seaborn as sns

# 设置中文字体
plt.rcParams['font.family'] = 'SimHei'
plt.rcParams['axes.unicode_minus'] = False

def create_complex_maze():
    """创建复杂迷宫，确保从起点到终点有路径"""
    maze = np.ones((20, 20))  # 1表示墙，0表示路径

    # 创建确保连通的迷宫结构
    paths = [
        # 主要连通路径：从(1,1)到(18,18)
        [(1,1), (1,2), (1,3), (1,4), (1,5), (1,6), (1,7), (1,8), (1,9), (1,10)],
        [(1,10), (2,10), (3,10), (4,10), (5,10), (6,10), (7,10), (8,10)],
        [(8,10), (8,11), (8,12), (8,13), (8,14), (8,15), (8,16), (8,17), (8,18)],
        [(8,18), (9,18), (10,18), (11,18), (12,18), (13,18), (14,18), (15,18), (16,18), (17,18), (18,18)],

        # 分支路径1
        [(1,3), (2,3), (3,3), (4,3), (5,3), (6,3), (7,3)],
        [(4,3), (4,4), (4,5), (4,6), (4,7)],
        [(6,3), (6,4), (6,5), (6,6)],

        # 分支路径2
        [(3,10), (3,11), (3,12), (3,13), (3,14)],
        [(5,10), (5,9), (5,8), (5,7), (5,6), (5,5)],
        [(7,10), (7,9), (7,8), (7,7)],

        # 分支路径3
        [(8,14), (9,14), (10,14), (11,14), (12,14)],
        [(10,14), (10,15), (10,16), (10,17)],
        [(12,14), (12,13), (12,12), (12,11), (12,10)],

        # 分支路径4
        [(11,18), (11,17), (11,16), (11,15)],
        [(13,18), (13,17), (13,16), (13,15), (13,14)],
        [(15,18), (15,17), (15,16), (15,15), (15,14), (15,13)],

        # 额外的复杂路径
        [(1,7), (2,7), (3,7), (3,8), (3,9)],
        [(5,5), (6,5), (7,5), (8,5), (9,5), (10,5)],
        [(10,5), (10,6), (10,7), (10,8), (10,9)],
        [(12,10), (13,10), (14,10), (15,10), (16,10)],
        [(16,10), (16,11), (16,12), (16,13), (16,14), (16,15)],

        # 死胡同
        [(2,7), (2,6), (2,5)],
        [(9,5), (9,4), (9,3), (9,2)],
        [(14,10), (14,9), (14,8)],
        [(16,15), (17,15), (18,15)],
    ]

    # 设置路径
    for path in paths:
        for x, y in path:
            if 0 <= x < 20 and 0 <= y < 20:
                maze[x, y] = 0

    return maze

def a_star_pathfinding(maze, start, goal):
    """A*寻路算法"""
    def heuristic(a, b):
        return abs(a[0] - b[0]) + abs(a[1] - b[1])
    
    def get_neighbors(pos):
        x, y = pos
        neighbors = []
        for dx, dy in [(0,1), (1,0), (0,-1), (-1,0)]:
            nx, ny = x + dx, y + dy
            if 0 <= nx < maze.shape[0] and 0 <= ny < maze.shape[1] and maze[nx, ny] == 0:
                neighbors.append((nx, ny))
        return neighbors
    
    open_set = [(0, start)]
    came_from = {}
    g_score = {start: 0}
    f_score = {start: heuristic(start, goal)}
    
    visited = set()
    
    while open_set:
        current = min(open_set, key=lambda x: f_score.get(x[1], float('inf')))[1]
        open_set = [item for item in open_set if item[1] != current]
        
        if current == goal:
            path = []
            while current in came_from:
                path.append(current)
                current = came_from[current]
            path.append(start)
            return path[::-1], visited
        
        visited.add(current)
        
        for neighbor in get_neighbors(current):
            tentative_g_score = g_score[current] + 1
            
            if neighbor not in g_score or tentative_g_score < g_score[neighbor]:
                came_from[neighbor] = current
                g_score[neighbor] = tentative_g_score
                f_score[neighbor] = g_score[neighbor] + heuristic(neighbor, goal)
                if (f_score[neighbor], neighbor) not in open_set:
                    open_set.append((f_score[neighbor], neighbor))
    
    return [], visited

def dijkstra_pathfinding(maze, start, goal):
    """Dijkstra寻路算法"""
    def get_neighbors(pos):
        x, y = pos
        neighbors = []
        for dx, dy in [(0,1), (1,0), (0,-1), (-1,0)]:
            nx, ny = x + dx, y + dy
            if 0 <= nx < maze.shape[0] and 0 <= ny < maze.shape[1] and maze[nx, ny] == 0:
                neighbors.append((nx, ny))
        return neighbors
    
    distances = {start: 0}
    previous = {}
    unvisited = set()
    visited = set()
    
    # 初始化所有可达位置
    for i in range(maze.shape[0]):
        for j in range(maze.shape[1]):
            if maze[i, j] == 0:
                if (i, j) != start:
                    distances[(i, j)] = float('inf')
                unvisited.add((i, j))
    
    while unvisited:
        current = min(unvisited, key=lambda x: distances.get(x, float('inf')))
        unvisited.remove(current)
        visited.add(current)
        
        if current == goal:
            path = []
            while current in previous:
                path.append(current)
                current = previous[current]
            path.append(start)
            return path[::-1], visited
        
        for neighbor in get_neighbors(current):
            if neighbor in unvisited:
                alt = distances[current] + 1
                if alt < distances[neighbor]:
                    distances[neighbor] = alt
                    previous[neighbor] = current
    
    return [], visited

def bfs_pathfinding(maze, start, goal):
    """BFS寻路算法 - 错误：使用栈而不是队列"""
    def get_neighbors(pos):
        x, y = pos
        neighbors = []
        for dx, dy in [(0,1), (1,0), (0,-1), (-1,0)]:
            nx, ny = x + dx, y + dy
            if 0 <= nx < maze.shape[0] and 0 <= ny < maze.shape[1] and maze[nx, ny] == 0:
                neighbors.append((nx, ny))
        return neighbors
    
    # 错误：使用列表作为栈，而不是deque作为队列
    stack = [start]  # 这实际上变成了DFS
    visited = {start}
    parent = {start: None}
    
    while stack:
        current = stack.pop()  # 错误：pop()从末尾取，变成了DFS
        
        if current == goal:
            path = []
            while current is not None:
                path.append(current)
                current = parent[current]
            return path[::-1], visited
        
        for neighbor in get_neighbors(current):
            if neighbor not in visited:
                visited.add(neighbor)
                parent[neighbor] = current
                stack.append(neighbor)  # 错误：append到末尾，pop从末尾取，变成了DFS
    
    return [], visited

def visualize_pathfinding_comparison():
    """可视化寻路算法对比"""
    maze = create_complex_maze()
    start = (1, 1)
    goal = (18, 18)
    
    # 运行三种算法
    astar_path, astar_visited = a_star_pathfinding(maze, start, goal)
    dijkstra_path, dijkstra_visited = dijkstra_pathfinding(maze, start, goal)
    bfs_path, bfs_visited = bfs_pathfinding(maze, start, goal)
    
    # 创建图形
    fig = plt.figure(figsize=(20, 16))
    
    # 定义颜色
    colors = ['white', 'black', 'lightblue', 'red', 'green', 'orange']
    cmap = ListedColormap(colors)
    
    algorithms = [
        ('A* 算法', astar_path, astar_visited),
        ('Dijkstra 算法', dijkstra_path, dijkstra_visited),
        ('BFS 算法', bfs_path, bfs_visited)
    ]
    
    for i, (name, path, visited) in enumerate(algorithms):
        ax = plt.subplot(2, 3, i+1)
        
        # 创建可视化矩阵
        vis_maze = maze.copy()
        
        # 标记访问过的节点
        for pos in visited:
            if pos != start and pos != goal:
                vis_maze[pos] = 2
        
        # 标记路径
        for pos in path:
            if pos != start and pos != goal:
                vis_maze[pos] = 3
        
        # 标记起点和终点
        vis_maze[start] = 4
        vis_maze[goal] = 5
        
        im = ax.imshow(vis_maze, cmap=cmap, vmin=0, vmax=5)
        ax.set_title(f'{name}\n路径长度: {len(path)}, 访问节点: {len(visited)}', fontsize=12, fontweight='bold')
        ax.set_xticks([])
        ax.set_yticks([])
        
        # 添加网格
        for x in range(21):
            ax.axhline(x-0.5, color='gray', linewidth=0.5, alpha=0.3)
            ax.axvline(x-0.5, color='gray', linewidth=0.5, alpha=0.3)
    
    # 性能对比图
    ax4 = plt.subplot(2, 3, 4)
    metrics = ['路径长度', '访问节点数', '时间复杂度(相对)']
    astar_metrics = [len(astar_path), len(astar_visited), 1.2]
    dijkstra_metrics = [len(dijkstra_path), len(dijkstra_visited), 2.1]
    bfs_metrics = [len(bfs_path), len(bfs_visited), 1.8]
    
    x = np.arange(len(metrics))
    width = 0.25
    
    ax4.bar(x - width, astar_metrics, width, label='A*', color='red', alpha=0.7)
    ax4.bar(x, dijkstra_metrics, width, label='Dijkstra', color='green', alpha=0.7)
    ax4.bar(x + width, bfs_metrics, width, label='BFS', color='orange', alpha=0.7)
    
    ax4.set_xlabel('性能指标')
    ax4.set_ylabel('数值')
    ax4.set_title('算法性能对比')
    ax4.set_xticks(x)
    ax4.set_xticklabels(metrics)
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    # 算法复杂度分析
    ax5 = plt.subplot(2, 3, 5)
    complexity_data = {
        'A*': {'时间': 'O(b^d)', '空间': 'O(b^d)', '最优性': '是', '完备性': '是'},
        'Dijkstra': {'时间': 'O(V²)', '空间': 'O(V)', '最优性': '是', '完备性': '是'},
        'BFS': {'时间': 'O(V+E)', '空间': 'O(V)', '最优性': '是*', '完备性': '是'}
    }
    
    table_data = []
    for alg in ['A*', 'Dijkstra', 'BFS']:
        row = [alg, complexity_data[alg]['时间'], complexity_data[alg]['空间'], 
               complexity_data[alg]['最优性'], complexity_data[alg]['完备性']]
        table_data.append(row)
    
    ax5.axis('tight')
    ax5.axis('off')
    table = ax5.table(cellText=table_data,
                     colLabels=['算法', '时间复杂度', '空间复杂度', '最优性', '完备性'],
                     cellLoc='center',
                     loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1.2, 1.5)
    ax5.set_title('算法理论分析', fontsize=12, fontweight='bold', pad=20)
    
    # 启发式函数可视化
    ax6 = plt.subplot(2, 3, 6)
    heuristic_map = np.zeros_like(maze, dtype=float)
    for i in range(maze.shape[0]):
        for j in range(maze.shape[1]):
            if maze[i, j] == 0:  # 只计算可通行区域
                heuristic_map[i, j] = abs(i - goal[0]) + abs(j - goal[1])
            else:
                heuristic_map[i, j] = np.nan
    
    im6 = ax6.imshow(heuristic_map, cmap='viridis', alpha=0.8)
    ax6.set_title('A*启发式函数热力图\n(曼哈顿距离到目标点)', fontsize=12, fontweight='bold')
    ax6.set_xticks([])
    ax6.set_yticks([])
    plt.colorbar(im6, ax=ax6, shrink=0.8)
    
    # 标记起点和终点
    ax6.plot(start[1], start[0], 'ro', markersize=10, label='起点')
    ax6.plot(goal[1], goal[0], 'go', markersize=10, label='终点')
    ax6.legend()
    
    plt.tight_layout()
    plt.savefig('pathfinding_algorithms_comparison_C.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    visualize_pathfinding_comparison()
